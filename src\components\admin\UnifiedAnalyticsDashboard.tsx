import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  GraduationCap, 
  FileText, 
  BarChart3, 
  Download, 
  Search, 
  Loader2,
  Globe,
  UserCheck,
  Briefcase,
  Calendar,
  TrendingUp,
  Database
} from 'lucide-react';
import { getUnifiedAnalytics } from '@/services/unifiedAnalyticsService';
import { SchoolUnifiedData, UnifiedAnalyticsOverview } from '@/types/unified-analytics';
import { useToast } from '@/hooks/use-toast';
import { useUserRole } from '@/hooks/useUserRole';
import { 
  downloadSchoolUnifiedCSV, 
  downloadAllUnifiedCSV, 
  downloadSchoolSummaryCSV 
} from '@/lib/unifiedCsvExport';

const UnifiedAnalyticsDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const { isTeacher } = useUserRole();

  const { data: analytics, isLoading, error, refetch } = useQuery({
    queryKey: ['unified-analytics'],
    queryFn: getUnifiedAnalytics,
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: 1000
  });

  // Filter schools based on search term
  const filteredSchools = analytics?.schools?.filter(school =>
    school.schoolName.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleDownloadSchool = (school: SchoolUnifiedData) => {
    try {
      downloadSchoolUnifiedCSV(school);
      toast({
        title: "Download started",
        description: `Downloading unified analytics for ${school.schoolName}`,
      });
    } catch (error) {
      console.error('Error downloading school data:', error);
      toast({
        title: "Download failed",
        description: "Failed to download school data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadAll = () => {
    if (!analytics?.schools) return;
    
    try {
      downloadAllUnifiedCSV(analytics.schools);
      toast({
        title: "Download started",
        description: "Downloading unified analytics for all schools",
      });
    } catch (error) {
      console.error('Error downloading all data:', error);
      toast({
        title: "Download failed",
        description: "Failed to download all data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadSummary = () => {
    if (!analytics?.schools) return;
    
    try {
      downloadSchoolSummaryCSV(analytics.schools);
      toast({
        title: "Download started",
        description: "Downloading school summary analytics",
      });
    } catch (error) {
      console.error('Error downloading summary:', error);
      toast({
        title: "Download failed",
        description: "Failed to download summary. Please try again.",
        variant: "destructive",
      });
    }
  };

  const renderBreakdownCard = (
    title: string,
    icon: React.ReactNode,
    data: Record<string, number>,
    total: number
  ) => {
    const entries = Object.entries(data).sort(([,a], [,b]) => b - a);
    const topEntries = entries.slice(0, 3);

    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {icon}
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {topEntries.map(([key, value]) => (
              <div key={key} className="flex justify-between text-sm">
                <span className="truncate">{key}</span>
                <span className="font-medium">
                  {value} ({Math.round((value / total) * 100)}%)
                </span>
              </div>
            ))}
            {entries.length > 3 && (
              <div className="text-xs text-muted-foreground">
                +{entries.length - 3} more
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isTeacher) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Access Restricted</h3>
          <p className="text-muted-foreground">Only teachers can access unified analytics.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading unified analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-destructive">Error Loading Analytics</h3>
          <p className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Failed to load unified analytics'}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!analytics || analytics.totalStudents === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold">No Data Available</h3>
          <p className="text-muted-foreground">
            No student data found. Students need to complete demographics and tests.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Unified Student Analytics</h2>
          <p className="text-muted-foreground">
            Combined demographic and test performance data organized by school
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleDownloadSummary}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Summary Report
          </Button>
          <Button
            onClick={handleDownloadAll}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download All Data
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              Across {analytics.totalSchools} schools
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Demographics</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.studentsWithDemographics}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((analytics.studentsWithDemographics / analytics.totalStudents) * 100)}% completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Test Results</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.studentsWithTests}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((analytics.studentsWithTests / analytics.totalStudents) * 100)}% completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Complete Profiles</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.studentsWithBoth}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((analytics.studentsWithBoth / analytics.totalStudents) * 100)}% have both
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Overall Breakdown Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.keys(analytics.overallDemographicBreakdown.by_country).length > 0 &&
          renderBreakdownCard(
            'Overall by Country',
            <Globe className="h-4 w-4 text-muted-foreground" />,
            analytics.overallDemographicBreakdown.by_country,
            analytics.studentsWithDemographics
          )
        }

        {Object.keys(analytics.overallDemographicBreakdown.by_role).length > 0 &&
          renderBreakdownCard(
            'Overall by Role',
            <Briefcase className="h-4 w-4 text-muted-foreground" />,
            analytics.overallDemographicBreakdown.by_role,
            analytics.studentsWithDemographics
          )
        }
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search schools..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* School-wise Analytics */}
      {filteredSchools.length > 0 ? (
        <div className="space-y-6">
          {filteredSchools.map((school) => (
            <Card key={school.schoolName} className="overflow-hidden">
              <CardHeader className="bg-muted/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <GraduationCap className="h-5 w-5 text-primary" />
                    <div>
                      <CardTitle className="text-lg">{school.schoolName}</CardTitle>
                      <CardDescription>
                        {school.totalStudents} students • {school.studentsWithBoth} complete profiles
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadSchool(school)}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download CSV
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="p-6">
                {/* School Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="text-2xl font-bold text-primary">{school.studentsWithDemographics}</div>
                    <div className="text-sm text-muted-foreground">Demographics</div>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="text-2xl font-bold text-primary">{school.studentsWithTests}</div>
                    <div className="text-sm text-muted-foreground">Test Results</div>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="text-2xl font-bold text-primary">{school.testBreakdown.totalTests}</div>
                    <div className="text-sm text-muted-foreground">Total Tests</div>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="text-2xl font-bold text-primary">{school.testBreakdown.averageTestsPerStudent}</div>
                    <div className="text-sm text-muted-foreground">Avg Tests/Student</div>
                  </div>
                </div>

                {/* Demographic Breakdown for School */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.keys(school.demographicBreakdown.by_country).length > 0 &&
                    renderBreakdownCard(
                      'Countries',
                      <Globe className="h-4 w-4 text-muted-foreground" />,
                      school.demographicBreakdown.by_country,
                      school.studentsWithDemographics
                    )
                  }

                  {Object.keys(school.demographicBreakdown.by_gender).length > 0 &&
                    renderBreakdownCard(
                      'Gender',
                      <Users className="h-4 w-4 text-muted-foreground" />,
                      school.demographicBreakdown.by_gender,
                      school.studentsWithDemographics
                    )
                  }

                  {Object.keys(school.demographicBreakdown.by_role).length > 0 &&
                    renderBreakdownCard(
                      'Role Type',
                      <Briefcase className="h-4 w-4 text-muted-foreground" />,
                      school.demographicBreakdown.by_role,
                      school.studentsWithDemographics
                    )
                  }
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold">No schools found</h3>
          <p className="text-muted-foreground">
            {searchTerm ? `No schools match "${searchTerm}"` : 'No schools available'}
          </p>
        </div>
      )}
    </div>
  );
};

export default UnifiedAnalyticsDashboard;
