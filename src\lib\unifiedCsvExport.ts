import { SchoolUnifiedData, UnifiedStudentData, UnifiedExportData } from '@/types/unified-analytics';

/**
 * Convert unified student data to CSV export format
 */
export function convertUnifiedDataToExportFormat(
  students: UnifiedStudentData[],
  schoolName: string
): UnifiedExportData[] {
  return students.map(student => {
    const demographic = student.demographicData;
    const testResults = student.testResults;

    // Count test types
    const preTestsCompleted = testResults.filter(t => t.testType === 'pre_test').length;
    const postTestsCompleted = testResults.filter(t => t.testType === 'post_test').length;

    // Get latest test date
    const latestTestDate = testResults.length > 0 
      ? testResults.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())[0].submittedAt
      : undefined;

    return {
      schoolName,
      studentId: student.student.id,
      studentName: student.student.fullName,
      email: student.student.email,
      // Demographic fields
      demographicCompletedAt: demographic?.completedAt,
      consent: demographic?.responses.consent as string,
      country: demographic?.responses.country as string,
      gender: demographic?.responses.gender as string,
      age: demographic?.responses.age as number,
      formal_training: demographic?.responses.formal_training as string,
      role_type: demographic?.responses.role_type as string,
      student_level: demographic?.responses.student_level as string,
      university: demographic?.responses.university as string,
      undergraduate_program: demographic?.responses.undergraduate_program as string,
      undergraduate_year: demographic?.responses.undergraduate_year as string,
      postgraduate_program: demographic?.responses.postgraduate_program as string,
      practitioner_work: demographic?.responses.practitioner_work as string,
      workplace: demographic?.responses.workplace as string,
      location: demographic?.responses.location as string,
      experience_years: demographic?.responses.experience_years as string,
      // Test results summary
      totalTestsCompleted: student.totalTestsCompleted,
      preTestsCompleted,
      postTestsCompleted,
      latestTestDate,
      testDetails: JSON.stringify(testResults.map(t => ({
        testTitle: t.testTitle,
        testType: t.testType,
        moduleTitle: t.moduleTitle,
        courseTitle: t.courseTitle,
        submittedAt: t.submittedAt,
        responseCount: t.responseCount
      })))
    };
  });
}

/**
 * Convert unified export data to CSV string
 */
export function convertUnifiedExportDataToCSV(exportData: UnifiedExportData[]): string {
  if (!exportData || exportData.length === 0) {
    return 'No data available';
  }

  // Define CSV headers
  const headers = [
    'School Name',
    'Student ID',
    'Student Name',
    'Email',
    'Demographic Completed At',
    'Consent',
    'Country',
    'Gender',
    'Age',
    'Formal Training',
    'Role Type',
    'Student Level',
    'University',
    'Undergraduate Program',
    'Undergraduate Year',
    'Postgraduate Program',
    'Practitioner Work',
    'Workplace',
    'Location',
    'Experience Years',
    'Total Tests Completed',
    'Pre-Tests Completed',
    'Post-Tests Completed',
    'Latest Test Date',
    'Test Details (JSON)'
  ];

  // Create CSV content
  const csvContent = [
    headers.join(','),
    ...exportData.map(row => {
      const values = [
        row.schoolName,
        row.studentId,
        row.studentName,
        row.email,
        row.demographicCompletedAt ? new Date(row.demographicCompletedAt).toLocaleDateString() : '',
        row.consent || '',
        row.country || '',
        row.gender || '',
        row.age || '',
        row.formal_training || '',
        row.role_type || '',
        row.student_level || '',
        row.university || '',
        row.undergraduate_program || '',
        row.undergraduate_year || '',
        row.postgraduate_program || '',
        row.practitioner_work || '',
        row.workplace || '',
        row.location || '',
        row.experience_years || '',
        row.totalTestsCompleted,
        row.preTestsCompleted,
        row.postTestsCompleted,
        row.latestTestDate ? new Date(row.latestTestDate).toLocaleDateString() : '',
        `"${row.testDetails.replace(/"/g, '""')}"` // Escape quotes in JSON
      ];

      // Escape values that contain commas or quotes
      return values.map(value => {
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      }).join(',');
    })
  ].join('\n');

  return csvContent;
}

/**
 * Download unified analytics CSV for a specific school
 */
export function downloadSchoolUnifiedCSV(schoolData: SchoolUnifiedData): void {
  const exportData = convertUnifiedDataToExportFormat(schoolData.students, schoolData.schoolName);
  const csvContent = convertUnifiedExportDataToCSV(exportData);
  
  // Create filename with school name and timestamp
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const sanitizedSchoolName = schoolData.schoolName.replace(/[^a-zA-Z0-9]/g, '_');
  const filename = `${sanitizedSchoolName}_Unified_Analytics_${timestamp}.csv`;

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download all unified analytics data as a single CSV file
 */
export function downloadAllUnifiedCSV(schools: SchoolUnifiedData[]): void {
  // Combine all students from all schools
  const allExportData: UnifiedExportData[] = schools.flatMap(school => 
    convertUnifiedDataToExportFormat(school.students, school.schoolName)
  );

  if (allExportData.length === 0) {
    return;
  }

  const csvContent = convertUnifiedExportDataToCSV(allExportData);
  
  // Create filename with timestamp
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const filename = `All_Schools_Unified_Analytics_${timestamp}.csv`;

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download summary statistics CSV for all schools
 */
export function downloadSchoolSummaryCSV(schools: SchoolUnifiedData[]): void {
  if (!schools || schools.length === 0) {
    return;
  }

  const headers = [
    'School Name',
    'Total Students',
    'Students with Demographics',
    'Students with Tests',
    'Students with Both',
    'Completion Rate (%)',
    'Total Tests',
    'Pre-Tests',
    'Post-Tests',
    'Avg Tests per Student',
    'Top Country',
    'Top Gender',
    'Top Role'
  ];

  const rows = schools.map(school => {
    const completionRate = school.totalStudents > 0 
      ? Math.round((school.studentsWithBoth / school.totalStudents) * 100) 
      : 0;

    // Get top demographic values
    const topCountry = Object.entries(school.demographicBreakdown.by_country)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
    const topGender = Object.entries(school.demographicBreakdown.by_gender)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
    const topRole = Object.entries(school.demographicBreakdown.by_role)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

    return [
      school.schoolName,
      school.totalStudents,
      school.studentsWithDemographics,
      school.studentsWithTests,
      school.studentsWithBoth,
      completionRate,
      school.testBreakdown.totalTests,
      school.testBreakdown.preTests,
      school.testBreakdown.postTests,
      school.testBreakdown.averageTestsPerStudent,
      topCountry,
      topGender,
      topRole
    ].map(value => {
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  });

  const csvContent = [headers.join(','), ...rows].join('\n');
  
  // Create filename with timestamp
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `School_Summary_Analytics_${timestamp}.csv`;

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}
